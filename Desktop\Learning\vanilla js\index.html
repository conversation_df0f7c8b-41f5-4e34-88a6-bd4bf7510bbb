<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard E-commerce</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .dashboard {
            padding: 30px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            transform: translateY(0);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .section {
            background: #f8f9ff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        
        .product-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .product-card:hover {
            transform: scale(1.02);
        }
        
        .product-card h4 {
            color: #4ECDC4;
            margin-bottom: 8px;
        }
        
        .price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #FF6B6B;
        }
        
        .category {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8rem;
            display: inline-block;
            margin-top: 8px;
        }
        
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            background: #4ECDC4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .filter-btn:hover {
            background: #45B7B8;
        }
        
        .filter-btn.active {
            background: #FF6B6B;
        }
        
        #results {
            margin-top: 20px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            border-left: 4px solid #4ECDC4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ Dashboard E-commerce</h1>
            <p>Maîtrise les Array Methods avec ce projet pratique !</p>
        </div>
        
        <div class="dashboard">
            <div class="stats">
                <div class="stat-card">
                    <h3>Total Produits</h3>
                    <div class="number" id="totalProducts"></div>
                </div>
                <div class="stat-card">
                    <h3>Chiffre d'Affaires</h3>
                    <div class="number" id="totalRevenue">0€</div>
                </div>
                <div class="stat-card">
                    <h3>Prix Moyen</h3>
                    <div class="number" id="averagePrice">0€</div>
                </div>
                <div class="stat-card">
                    <h3>En Stock</h3>
                    <div class="number" id="inStock">0</div>
                </div>
            </div>
            
            <div class="section">
                <h2>🔍 Filtres</h2>
                <div class="filters">
                    <button class="filter-btn active" onclick="showAll()">Tous</button>
                    <button class="filter-btn" onclick="filterByCategory('Electronics')">Electronics</button>
                    <button class="filter-btn" onclick="filterByCategory('Clothing')">Clothing</button>
                    <button class="filter-btn" onclick="filterByCategory('Books')">Books</button>
                    <button class="filter-btn" onclick="showExpensive()">+ 50€</button>
                    <button class="filter-btn" onclick="showInStock()">En Stock</button>
                </div>
            </div>
            
            <div class="section">
                <h2>📊 Produits</h2>
                <div class="product-grid" id="productGrid">
                    <!-- Les produits seront affichés ici -->
                </div>
            </div>
            
            <div id="results"></div>
        </div>
    </div>

    <script>
        // DONNÉES DE TEST
        const products = [
            { id: 1, name: 'iPhone 13', category: 'Electronics', price: 899, stock: 15, inStock: true },
            { id: 2, name: 'MacBook Pro', category: 'Electronics', price: 1999, stock: 8, inStock: true },
            { id: 3, name: 'T-Shirt Nike', category: 'Clothing', price: 29, stock: 0, inStock: false },
            { id: 4, name: 'Jeans Levis', category: 'Clothing', price: 89, stock: 12, inStock: true },
            { id: 5, name: 'JavaScript: The Good Parts', category: 'Books', price: 25, stock: 20, inStock: true },
            { id: 6, name: 'Clean Code', category: 'Books', price: 35, stock: 5, inStock: true },
            { id: 7, name: 'AirPods Pro', category: 'Electronics', price: 249, stock: 0, inStock: false },
            { id: 8, name: 'Sneakers Adidas', category: 'Clothing', price: 120, stock: 18, inStock: true },
            { id: 9, name: 'You Don\'t Know JS', category: 'Books', price: 45, stock: 10, inStock: true },
            { id: 10, name: 'Samsung Galaxy', category: 'Electronics', price: 799, stock: 7, inStock: true }
        ];

        // ========== TES MISSIONS ==========
        
        // MISSION 1: Calculer et afficher les statistiques
        function updateStats() {
            // TODO: Utilise map, filter, reduce pour calculer :
            // - Nombre total de produits
            // - Chiffre d'affaires total (prix * stock)
            // - Prix moyen
            // - Nombre de produits en stock
            
            // AIDE: 
            // document.getElementById('totalProducts').textContent = ...
            // document.getElementById('totalRevenue').textContent = ... + '€'
            // etc.
        }
        
        // MISSION 2: Afficher tous les produits
        function displayProducts(productsToShow = products) {
            const grid = document.getElementById('productGrid');
            
            // TODO: Utilise map pour créer le HTML de chaque produit
            // et innerHTML pour l'afficher
            
            // MODÈLE HTML pour chaque produit :
            /*
            <div class="product-card">
                <h4>${product.name}</h4>
                <div class="price">${product.price}€</div>
                <p>Stock: ${product.stock}</p>
                <span class="category">${product.category}</span>
            </div>
            */
        }
        
        // MISSION 3: Filtrer par catégorie
        function filterByCategory(category) {
            // TODO: Utilise filter pour afficher uniquement les produits de cette catégorie
        }
        
        // MISSION 4: Afficher les produits chers (> 50€)
        function showExpensive() {
            // TODO: Utilise filter pour les produits > 50€
        }
        
        // MISSION 5: Afficher les produits en stock
        function showInStock() {
            // TODO: Utilise filter pour les produits avec inStock: true
        }
        
        // MISSION 6: Afficher tous les produits
        function showAll() {
            displayProducts(products);
            updateActiveButton(event.target);
        }
        
        // Helper function pour les boutons actifs
        function updateActiveButton(activeBtn) {
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            activeBtn.classList.add('active');
        }
        
        // BONUS MISSIONS (plus difficiles) :
        // - Créer une fonction de recherche par nom
        // - Ajouter un tri par prix (croissant/décroissant)
        // - Calculer la valeur totale du stock par catégorie
        
        // Initialisation
        updateStats();
        displayProducts();
        
        // Ajouter les event listeners pour les boutons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => updateActiveButton(e.target));
        });
    </script>
</body>
</html>